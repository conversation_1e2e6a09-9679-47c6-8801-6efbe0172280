#!/usr/bin/env python3
"""Debug script to test basic Tkinter canvas drawing."""

import tkinter as tk

def test_basic_canvas():
    root = tk.Tk()
    root.title("Debug Canvas Test")
    
    # Create canvas
    canvas = tk.Canvas(root, width=400, height=300, bg="white", highlightthickness=1)
    canvas.pack()
    
    # Draw some test shapes
    print("Drawing test shapes...")
    
    # Draw a red rectangle
    canvas.create_rectangle(50, 50, 150, 100, fill="red", outline="black")
    
    # Draw grid lines
    for i in range(0, 400, 20):
        canvas.create_line(i, 0, i, 300, fill="gray")
    for i in range(0, 300, 20):
        canvas.create_line(0, i, 400, i, fill="gray")
    
    # Draw some black squares (like life cells)
    canvas.create_rectangle(100, 100, 120, 120, fill="black")
    canvas.create_rectangle(120, 100, 140, 120, fill="black")
    canvas.create_rectangle(140, 100, 160, 120, fill="black")
    
    print("Canvas created with test shapes. You should see:")
    print("- A red rectangle")
    print("- Gray grid lines")
    print("- Three black squares")
    
    root.mainloop()

if __name__ == "__main__":
    test_basic_canvas()

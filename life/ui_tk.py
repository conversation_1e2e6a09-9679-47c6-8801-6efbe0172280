from __future__ import annotations
import tkinter as tk
import random
from .model import LifeModel


class LifeTk:
    def __init__(
        self, rows: int = 20, cols: int = 30, cell_size: int = 20, speed_ms: int = 200
    ):
        self.model = LifeModel(rows, cols)
        self.cell_size = cell_size
        self.speed_ms = speed_ms
        self.running = False

        self.root = tk.Tk()
        self.root.title(
            "Game of Life - Click cells, press 'r' to run, 'c' to clear, 'n' for random"
        )

        # Force window size and make it visible
        w, h = cols * cell_size, rows * cell_size
        self.root.geometry(f"{w + 20}x{h + 50}")
        self.root.resizable(False, False)

        # Create frame for canvas
        frame = tk.Frame(self.root, bg="gray")
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.canvas = tk.Canvas(
            frame, width=w, height=h, bg="white", bd=2, relief="sunken"
        )
        self.canvas.pack()

        # Make canvas focusable for keyboard events
        self.canvas.focus_set()

        # Draw everything
        self._draw_grid_lines(rows, cols)
        self._add_initial_pattern()
        self._draw_cells()

        # Force window to front and update
        self.root.lift()
        self.root.attributes("-topmost", True)
        self.root.after_idle(lambda: self.root.attributes("-topmost", False))
        self.root.update()

        print(f"Window created: {w}x{h} pixels")
        print("If you can't see the window, try Alt+Tab or check your dock")

        self.canvas.bind("<Button-1>", self._on_click)
        self.canvas.bind("<KeyPress-space>", self._on_step)
        self.canvas.bind("<KeyPress-r>", self._on_toggle_run)
        self.canvas.bind("<KeyPress-c>", self._on_clear)
        self.canvas.bind("<KeyPress-n>", self._on_random)
        self.canvas.bind("<KeyPress-q>", lambda e: self.root.destroy())

    def _draw_grid_lines(self, rows: int, cols: int):
        cs = self.cell_size
        print(f"Drawing grid: {rows}x{cols}, cell_size={cs}")
        # Draw more visible grid lines
        for r in range(rows + 1):
            y = r * cs
            self.canvas.create_line(0, y, cols * cs, y, fill="#888888", width=1)
        for c in range(cols + 1):
            x = c * cs
            self.canvas.create_line(x, 0, x, rows * cs, fill="#888888", width=1)
        print(f"Grid lines drawn: {rows + 1} horizontal, {cols + 1} vertical")

    def _add_initial_pattern(self):
        # Add a simple blinker pattern in the center so user sees something
        center_r, center_c = self.model._rows // 2, self.model._cols // 2
        self.model.set_cells(
            [(center_r - 1, center_c), (center_r, center_c), (center_r + 1, center_c)]
        )
        self._draw_cells()

    def _draw_cells(self):
        cs = self.cell_size
        self.canvas.delete("cell")
        count = 0
        for r, c in self.model._alive:
            x0, y0 = c * cs, r * cs
            x1, y1 = x0 + cs, y0 + cs
            self.canvas.create_rectangle(
                x0, y0, x1, y1, fill="black", outline="", tags="cell"
            )
            count += 1
        print(f"Cells drawn: {count}")
        print(f"Cells drawn: {len(self.model._alive)}")

    def _on_click(self, event):
        c = event.x // self.cell_size
        r = event.y // self.cell_size
        if 0 <= r < self.model._rows and 0 <= c < self.model._cols:
            self.model.toggle(r, c)
            self._draw_cells()

    def _on_step(self, _event=None):
        if not self.running:
            self.model.step()
            self._draw_cells()

    def _on_toggle_run(self, _event=None):
        self.running = not self.running
        if self.running:
            self._run_loop()

    def _on_clear(self, _event=None):
        self.model.clear()
        self._draw_cells()

    def _on_random(self, _event=None):
        self.model.randomize(0.2)
        self._draw_cells()

    def _run_loop(self):
        if self.running:
            self.model.step()
            self._draw_cells()
            self.root.after(self.speed_ms, self._run_loop)

    def run(self):
        self.root.mainloop()


if __name__ == "__main__":
    LifeTk().run()

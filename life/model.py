from __future__ import annotations
from typing import Iterable, Set, <PERSON>ple
import random

Coord = Tuple[int, int]


class LifeModel:
    """Minimal Game of Life model with a fixed rectangular board.

    - Board size: rows x cols
    - Alive cells stored as a set of (r, c)
    - No wrapping by default; neighbors outside bounds are ignored
    """

    def __init__(self, rows: int, cols: int):
        if rows <= 0 or cols <= 0:
            raise ValueError("rows and cols must be positive")
        self._rows = rows
        self._cols = cols
        self._alive: Set[Coord] = set()

    def clear(self) -> None:
        self._alive.clear()

    def randomize(self, p: float = 0.2, rng: random.Random | None = None) -> None:
        if rng is None:
            rng = random
        self._alive = {
            (r, c)
            for r in range(self._rows)
            for c in range(self._cols)
            if rng.random() < p
        }

    def toggle(self, r: int, c: int) -> None:
        self._check_bounds(r, c)
        if (r, c) in self._alive:
            self._alive.remove((r, c))
        else:
            self._alive.add((r, c))

    def set_cells(self, cells: Iterable[Coord]) -> None:
        s: Set[Coord] = set()
        for r, c in cells:
            self._check_bounds(r, c)
            s.add((r, c))
        self._alive = s

    def step(self) -> None:
        counts: dict[Coord, int] = {}
        for r, c in self._alive:
            for nr, nc in self._neighbors(r, c):
                counts[(nr, nc)] = counts.get((nr, nc), 0) + 1
        new_alive: Set[Coord] = set()
        for cell, n in counts.items():
            if n == 3 or (n == 2 and cell in self._alive):
                new_alive.add(cell)
        self._alive = new_alive

    def _neighbors(self, r: int, c: int) -> Iterable[Coord]:
        for dr in (-1, 0, 1):
            for dc in (-1, 0, 1):
                if dr == 0 and dc == 0:
                    continue
                nr, nc = r + dr, c + dc
                if 0 <= nr < self._rows and 0 <= nc < self._cols:
                    yield (nr, nc)

    def _check_bounds(self, r: int, c: int) -> None:
        if not (0 <= r < self._rows and 0 <= c < self._cols):
            raise IndexError("cell out of bounds")

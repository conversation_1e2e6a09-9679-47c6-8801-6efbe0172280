from __future__ import annotations
import pygame

from .model import LifeModel


class LifePygame:
    def __init__(self, rows: int = 30, cols: int = 40, cell_size: int = 20):
        self.model = LifeModel(rows, cols)
        self.cell_size = cell_size
        self.running_sim = False

        # Colors
        self.WHITE = (255, 255, 255)
        self.BLACK = (0, 0, 0)
        self.GRAY = (128, 128, 128)
        self.LIGHT_GRAY = (200, 200, 200)

        # Initialize pygame
        pygame.init()

        # Set up display
        self.width = cols * cell_size
        self.height = rows * cell_size + 50  # Extra space for instructions
        self.screen = pygame.display.set_mode((self.width, self.height))
        pygame.display.set_caption(
            "Game of Life - Click cells, SPACE=step, R=run/pause, C=clear, N=random"
        )

        # Font for instructions
        self.font = pygame.font.Font(None, 24)

        # Clock for controlling frame rate
        self.clock = pygame.time.Clock()

        # Add initial pattern
        self._add_initial_pattern()

    def _add_initial_pattern(self):
        # Add a blinker in the center
        center_r, center_c = self.model._rows // 2, self.model._cols // 2
        self.model.set_cells(
            [(center_r - 1, center_c), (center_r, center_c), (center_r + 1, center_c)]
        )

    def _draw_grid(self):
        cs = self.cell_size
        # Draw vertical lines
        for c in range(self.model._cols + 1):
            x = c * cs
            pygame.draw.line(self.screen, self.GRAY, (x, 0), (x, self.model._rows * cs))

        # Draw horizontal lines
        for r in range(self.model._rows + 1):
            y = r * cs
            pygame.draw.line(self.screen, self.GRAY, (0, y), (self.model._cols * cs, y))

    def _draw_cells(self):
        cs = self.cell_size
        for r, c in self.model._alive:
            x = c * cs
            y = r * cs
            pygame.draw.rect(self.screen, self.BLACK, (x, y, cs, cs))

    def _draw_instructions(self):
        y_offset = self.model._rows * self.cell_size + 5
        instructions = [
            "Click: toggle cell | SPACE: step | R: run/pause | C: clear | N: random | ESC: quit"
        ]
        for i, text in enumerate(instructions):
            surface = self.font.render(text, True, self.BLACK)
            self.screen.blit(surface, (5, y_offset + i * 25))

    def _handle_click(self, pos):
        x, y = pos
        if y < self.model._rows * self.cell_size:  # Only handle clicks in grid area
            c = x // self.cell_size
            r = y // self.cell_size
            if 0 <= r < self.model._rows and 0 <= c < self.model._cols:
                self.model.toggle(r, c)

    def run(self):
        running = True
        step_timer = 0
        step_delay = 500  # milliseconds between steps when running

        print("Pygame window should be visible!")
        print("Controls:")
        print("  Click: toggle cell")
        print("  SPACE: step once")
        print("  R: run/pause simulation")
        print("  C: clear all")
        print("  N: random pattern")
        print("  ESC: quit")

        while running:
            dt = self.clock.tick(60)  # 60 FPS

            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
                    elif event.key == pygame.K_SPACE:
                        if not self.running_sim:
                            self.model.step()
                    elif event.key == pygame.K_r:
                        self.running_sim = not self.running_sim
                        step_timer = 0
                    elif event.key == pygame.K_c:
                        self.model.clear()
                        self.running_sim = False
                    elif event.key == pygame.K_n:
                        self.model.randomize(0.2)
                        self.running_sim = False
                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 1:  # Left click
                        self._handle_click(event.pos)

            # Auto-step if running
            if self.running_sim:
                step_timer += dt
                if step_timer >= step_delay:
                    self.model.step()
                    step_timer = 0

            # Draw everything
            self.screen.fill(self.WHITE)
            self._draw_grid()
            self._draw_cells()
            self._draw_instructions()

            pygame.display.flip()

        pygame.quit()


if __name__ == "__main__":
    LifePygame().run()

#!/usr/bin/env python3
"""Absolutely minimal Tkinter test."""

import tkinter as tk

root = tk.Tk()
root.title("Minimal Test")
root.geometry("400x300")

# Add a label to see if anything shows up
label = tk.Label(root, text="Hello! Can you see this text?", font=("Arial", 16), bg="yellow")
label.pack(pady=20)

# Add a canvas with obvious content
canvas = tk.Canvas(root, width=300, height=200, bg="lightblue", bd=3, relief="raised")
canvas.pack(pady=10)

# Draw some very obvious shapes
canvas.create_rectangle(50, 50, 150, 100, fill="red", outline="black", width=3)
canvas.create_oval(200, 50, 250, 100, fill="green", outline="black", width=3)
canvas.create_text(150, 150, text="CANVAS TEXT", font=("Arial", 14), fill="blue")

print("Window should show:")
print("- Yellow label with 'Hello! Can you see this text?'")
print("- Light blue canvas")
print("- Red rectangle")
print("- Green circle")
print("- Blue text saying 'CANVAS TEXT'")

root.mainloop()

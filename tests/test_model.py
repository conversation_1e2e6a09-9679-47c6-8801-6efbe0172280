from life.model import LifeModel


def test_blinker_oscillates():
    m = LifeModel(5, 5)
    # Vertical blinker at center
    m.set_cells([(1, 2), (2, 2), (3, 2)])

    # Step 1 -> horizontal
    m.step()
    assert m.alive == {(2, 1), (2, 2), (2, 3)}

    # Step 2 -> vertical again
    m.step()
    assert m.alive == {(1, 2), (2, 2), (3, 2)}


def test_clear_and_toggle_bounds():
    m = LifeModel(3, 3)
    m.toggle(1, 1)
    assert (1, 1) in m.alive
    m.clear()
    assert not m.alive

    # Out of bounds
    try:
        m.toggle(5, 5)
    except IndexError:
        pass
    else:
        assert False, "Expected IndexError for out-of-bounds toggle"

